#!/usr/bin/env python3
"""
测试全新浏览器的控件识别
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def test_fresh_browser():
    """测试全新浏览器的控件识别"""
    print("=== 测试全新浏览器的控件识别 ===")
    print("请确保您已经完全关闭并重新打开了浏览器")
    
    # 创建UNI实例
    uni = UNI()
    
    x, y = 187, 46
    
    try:
        print("🔍 第一步：检查浏览器窗口状态...")
        
        # 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if not active_window:
            print("❌ 未找到活动窗口")
            return
        
        window_name = getattr(active_window, 'name', '')
        print(f"✅ 找到活动窗口: {window_name}")
        print(f"   进程ID: {processid}")
        print(f"   子控件数量: {windowChildCount}")
        
        # 检查是否为浏览器窗口
        is_browser = '浏览器' in window_name or 'browser' in window_name.lower()
        if not is_browser:
            print("❌ 当前窗口不是浏览器窗口")
            return
        
        print("✅ 确认这是浏览器窗口")
        
        # 直接测试子控件访问
        print(f"\n🔧 第二步：直接测试子控件访问...")
        
        for i in range(windowChildCount):
            try:
                print(f"   尝试访问子控件 {i}...")
                child = active_window.getChildAtIndex(i)
                if child:
                    child_name = getattr(child, 'name', 'unnamed')
                    child_role = child.getRoleName()
                    print(f"   ✅ 子控件 {i}: {child_name} ({child_role})")
                    print(f"      子控件的子控件数: {child.childCount}")
                    
                    # 如果子控件有子控件，尝试访问
                    if child.childCount > 0:
                        print(f"      🔍 尝试访问子控件的子控件...")
                        for j in range(min(child.childCount, 3)):
                            try:
                                grandchild = child.getChildAtIndex(j)
                                if grandchild:
                                    gc_name = getattr(grandchild, 'name', 'unnamed')
                                    gc_role = grandchild.getRoleName()
                                    print(f"        ✅ 孙控件 {j}: {gc_name} ({gc_role})")
                                else:
                                    print(f"        ❌ 孙控件 {j} 为空")
                            except Exception as e:
                                print(f"        ❌ 访问孙控件 {j} 失败: {e}")
                else:
                    print(f"   ❌ 子控件 {i} 为空")
                    print(f"   🚨 检测到子控件访问失败，这是需要修复的问题！")
                    
                    # 手动调用模拟展开
                    print(f"   🎯 手动调用模拟accerciser展开...")
                    success = uni._simulate_accerciser_expand(active_window, 0)
                    
                    if success:
                        print(f"   ✅ 模拟展开成功，重新测试子控件访问...")
                        child = active_window.getChildAtIndex(i)
                        if child:
                            child_name = getattr(child, 'name', 'unnamed')
                            child_role = child.getRoleName()
                            print(f"   ✅ 展开后子控件 {i}: {child_name} ({child_role})")
                        else:
                            print(f"   ❌ 展开后子控件 {i} 仍为空")
                    else:
                        print(f"   ❌ 模拟展开失败")
                        
            except Exception as e:
                print(f"   ❌ 访问子控件 {i} 异常: {e}")
        
        # 测试完整的控件识别
        print(f"\n🎯 第三步：测试完整的控件识别...")
        
        start_time = time.time()
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        end_time = time.time()
        
        print(f"⏱️  识别耗时: {end_time - start_time:.3f}秒")
        
        if result and not result.get('error'):
            print("✅ 控件识别成功!")
            print(f"   控件名称: {result.get('Name', 'N/A')}")
            print(f"   控件类型: {result.get('Rolename', 'N/A')}")
            print(f"   进程名称: {result.get('ProcessName', 'N/A')}")
            
            if 'button' in result.get('Rolename', '').lower():
                print("🔘 成功识别到按钮控件!")
                if '首页' in result.get('Name', '') or 'home' in result.get('Name', '').lower():
                    print("🏠 确认这是首页按钮!")
                    print("🎉 问题已解决!")
                else:
                    print(f"🔘 识别到其他按钮: {result.get('Name', 'N/A')}")
            else:
                print(f"❓ 识别到其他类型控件: {result.get('Rolename', 'N/A')}")
                print("⚠️ 问题仍然存在，需要进一步调试")
        else:
            print("❌ 控件识别失败!")
            if result:
                print(f"   错误: {result.get('error', '未知错误')}")
            print("⚠️ 问题仍然存在，需要进一步调试")
            
    except Exception as e:
        print(f"💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fresh_browser()
