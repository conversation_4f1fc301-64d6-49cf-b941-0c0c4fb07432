#!/usr/bin/env python3
"""
检查浏览器状态
"""
import sys
import os
import time
import subprocess

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def check_browser_status():
    """检查浏览器状态"""
    print("=== 检查浏览器状态 ===")
    
    try:
        # 检查浏览器进程
        print("🔍 第一步：检查浏览器进程...")
        
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        browser_processes = []
        
        for line in result.stdout.split('\n'):
            if 'qaxbrowser' in line.lower():
                browser_processes.append(line.strip())
        
        print(f"   找到 {len(browser_processes)} 个浏览器进程:")
        for i, process in enumerate(browser_processes):
            print(f"   {i+1}. {process}")
        
        if not browser_processes:
            print("❌ 没有找到浏览器进程，请确保浏览器已打开")
            return
        
        # 检查AT-SPI中的浏览器应用
        print(f"\n🔍 第二步：检查AT-SPI中的浏览器应用...")
        
        uni = UNI()
        import pyatspi
        
        desktop = uni._get_fresh_desktop()
        print(f"   桌面应用数量: {desktop.childCount}")
        
        browser_apps = []
        all_apps = []
        
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if not app:
                    continue
                    
                app_name = getattr(app, 'name', 'Unknown')
                all_apps.append(f"{i}: {app_name}")
                
                if '浏览器' in app_name or 'browser' in app_name.lower():
                    browser_apps.append({
                        'index': i,
                        'name': app_name,
                        'app': app
                    })
                    print(f"   ✅ 找到浏览器应用 {i}: {app_name}")
            except Exception as e:
                all_apps.append(f"{i}: 访问失败 - {e}")
        
        if not browser_apps:
            print("❌ AT-SPI中没有找到浏览器应用")
            print("   所有应用列表:")
            for app_info in all_apps:
                print(f"     {app_info}")
            
            print(f"\n🔧 尝试刷新AT-SPI注册表...")
            
            # 强制刷新AT-SPI
            time.sleep(1)
            desktop = pyatspi.Registry.getDesktop(0)
            print(f"   刷新后桌面应用数量: {desktop.childCount}")
            
            # 重新检查
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    if app:
                        app_name = getattr(app, 'name', 'Unknown')
                        if '浏览器' in app_name or 'browser' in app_name.lower():
                            print(f"   ✅ 刷新后找到浏览器应用 {i}: {app_name}")
                            browser_apps.append({
                                'index': i,
                                'name': app_name,
                                'app': app
                            })
                except:
                    continue
        
        if not browser_apps:
            print("❌ 刷新后仍未找到浏览器应用")
            print("💡 建议:")
            print("   1. 确保浏览器窗口在前台")
            print("   2. 尝试点击浏览器窗口")
            print("   3. 等待几秒钟后重试")
            print("   4. 检查浏览器的无障碍设置")
            return
        
        # 检查浏览器窗口
        print(f"\n🔍 第三步：检查浏览器窗口...")
        
        for browser_info in browser_apps:
            app = browser_info['app']
            app_name = browser_info['name']
            
            print(f"   浏览器应用: {app_name}")
            print(f"   子窗口数量: {app.childCount}")
            
            for j in range(app.childCount):
                try:
                    window = app.getChildAtIndex(j)
                    if not window:
                        print(f"     窗口 {j}: 为空")
                        continue
                    
                    window_name = getattr(window, 'name', 'Unknown')
                    window_role = window.getRoleName()
                    
                    print(f"     窗口 {j}: {window_name} ({window_role})")
                    
                    # 获取窗口状态
                    try:
                        states = window.getState()
                        state_names = [pyatspi.stateToString(state) for state in states.getStates()]
                        print(f"       状态: {', '.join(state_names)}")
                        
                        # 检查窗口位置
                        component = window.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            print(f"       位置: ({extents.x}, {extents.y}) 大小: {extents.width}x{extents.height}")
                            
                            # 检查坐标(187, 46)是否在窗口内
                            if (extents.x <= 187 <= extents.x + extents.width and 
                                extents.y <= 46 <= extents.y + extents.height):
                                print(f"       ✅ 坐标(187, 46)在此窗口内!")
                                
                                # 测试子控件访问
                                print(f"       🔧 测试子控件访问...")
                                print(f"         子控件数量: {window.childCount}")
                                
                                for k in range(min(window.childCount, 3)):
                                    try:
                                        child = window.getChildAtIndex(k)
                                        if child:
                                            child_name = getattr(child, 'name', 'unnamed')
                                            child_role = child.getRoleName()
                                            print(f"         ✅ 子控件 {k}: {child_name} ({child_role})")
                                        else:
                                            print(f"         ❌ 子控件 {k} 为空")
                                    except Exception as e:
                                        print(f"         ❌ 访问子控件 {k} 失败: {e}")
                    except Exception as e:
                        print(f"       ❌ 获取窗口信息失败: {e}")
                        
                except Exception as e:
                    print(f"     ❌ 访问窗口 {j} 失败: {e}")
        
        # 测试UNI控件识别
        print(f"\n🎯 第四步：测试UNI控件识别...")
        
        x, y = 187, 46
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        if result and not result.get('error'):
            print("✅ 控件识别成功!")
            print(f"   控件名称: {result.get('Name', 'N/A')}")
            print(f"   控件类型: {result.get('Rolename', 'N/A')}")
            print(f"   进程名称: {result.get('ProcessName', 'N/A')}")
            
            if result.get('ProcessName', '').lower() == 'qaxbrowser':
                print("🌐 成功识别到浏览器控件!")
            else:
                print(f"❓ 识别到的不是浏览器控件")
        else:
            print("❌ 控件识别失败!")
            
    except Exception as e:
        print(f"💥 检查过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_browser_status()
