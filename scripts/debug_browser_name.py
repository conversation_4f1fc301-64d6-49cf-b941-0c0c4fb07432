#!/usr/bin/env python3
"""
调试浏览器窗口名称检测
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def debug_browser_name():
    """调试浏览器窗口名称检测"""
    print("=== 调试浏览器窗口名称检测 ===")
    
    # 创建UNI实例
    uni = UNI()
    
    x, y = 187, 46
    
    try:
        # 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if active_window:
            window_name = getattr(active_window, 'name', '')
            print(f"🌐 活动窗口名称: '{window_name}'")
            print(f"   进程ID: {processid}")
            print(f"   窗口角色: {windowRoleName}")
            print(f"   子控件数: {windowChildCount}")
            
            # 检查浏览器名称匹配
            print(f"\n🔍 浏览器名称检测:")
            print(f"   包含'浏览器': {'浏览器' in window_name}")
            print(f"   包含'browser': {'browser' in window_name.lower()}")
            
            if '浏览器' in window_name or 'browser' in window_name.lower():
                print(f"   ✅ 检测为浏览器窗口")
            else:
                print(f"   ❌ 未检测为浏览器窗口")
            
            # 手动测试智能深度搜索
            print(f"\n🧠 手动测试智能深度搜索...")
            
            # 模拟智能深度搜索的递归函数
            candidates = []
            
            def test_search_recursive(current_element, depth=0, parent_info=None):
                if depth > 10:  # 限制深度
                    return
                
                try:
                    import pyatspi
                    if hasattr(current_element, 'queryComponent'):
                        component = current_element.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        
                        role = current_element.getRoleName().lower()
                        name = current_element.name if current_element.name else 'unnamed'
                        area = extents.width * extents.height
                        
                        # 检查坐标是否在元素范围内
                        contains_point = (extents.x <= x < extents.x + extents.width and
                                        extents.y <= y < extents.y + extents.height)
                        
                        if contains_point:
                            print(f"[DEBUG]   {'  ' * depth}找到候选控件: {name} ({role}) "
                                  f"面积:{area} 深度:{depth}", file=sys.stderr)
                            
                            candidates.append({
                                'element': current_element,
                                'role': role,
                                'name': name,
                                'area': area,
                                'depth': depth,
                                'extents': extents
                            })
                        
                        # 检查子控件访问
                        element_name = getattr(current_element, 'name', '')
                        print(f"[DEBUG]   {'  ' * depth}检查元素: {element_name} (子控件数: {current_element.childCount})", file=sys.stderr)
                        
                        if '浏览器' in element_name or 'browser' in element_name.lower():
                            print(f"[DEBUG]   {'  ' * depth}🌐 检测到浏览器元素，使用特殊访问方法", file=sys.stderr)
                            
                            # 测试子控件访问
                            for i in range(current_element.childCount):
                                try:
                                    child = current_element.getChildAtIndex(i)
                                    if child:
                                        child_name = getattr(child, 'name', 'unnamed')
                                        print(f"[DEBUG]   {'  ' * depth}✅ 浏览器子控件 {i}: {child_name}", file=sys.stderr)
                                        test_search_recursive(child, depth + 1, parent_info)
                                    else:
                                        print(f"[DEBUG]   {'  ' * depth}❌ 浏览器子控件 {i} 为空", file=sys.stderr)
                                except Exception as e:
                                    print(f"[DEBUG]   {'  ' * depth}❌ 浏览器子控件 {i} 访问失败: {e}", file=sys.stderr)
                        else:
                            print(f"[DEBUG]   {'  ' * depth}📋 普通元素，使用标准访问方法", file=sys.stderr)
                            
                            # 标准子控件访问
                            for i in range(current_element.childCount):
                                try:
                                    child = current_element.getChildAtIndex(i)
                                    if child:
                                        test_search_recursive(child, depth + 1, parent_info)
                                except:
                                    continue
                
                except Exception as e:
                    print(f"[DEBUG]   {'  ' * depth}搜索控件时出错: {e}", file=sys.stderr)
            
            # 开始测试搜索
            test_search_recursive(active_window)
            
            print(f"\n📊 搜索结果:")
            print(f"   找到候选控件数: {len(candidates)}")
            
            for i, candidate in enumerate(candidates):
                print(f"   {i+1}. {candidate['name']} ({candidate['role']}) 深度:{candidate['depth']}")
                if 'button' in candidate['role']:
                    print(f"      🔘 这是按钮控件!")
        else:
            print("❌ 未找到活动窗口")
            
    except Exception as e:
        print(f"💥 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_browser_name()
