#!/usr/bin/env python3
"""
测试强制刷新AT-SPI功能
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def test_force_refresh():
    """测试强制刷新AT-SPI功能"""
    print("=== 测试强制刷新AT-SPI功能 ===")
    print("请确保您已经重新打开了浏览器")
    
    # 创建UNI实例
    uni = UNI()
    
    x, y = 187, 46
    
    try:
        print("🔍 第一步：检查浏览器子控件访问状态...")
        
        # 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if not active_window:
            print("❌ 未找到活动窗口")
            return
        
        window_name = getattr(active_window, 'name', '')
        print(f"✅ 找到活动窗口: {window_name}")
        print(f"   子控件数量: {windowChildCount}")
        
        # 检查是否为浏览器窗口
        is_browser = '浏览器' in window_name or 'browser' in window_name.lower()
        if not is_browser:
            print("❌ 当前窗口不是浏览器窗口")
            return
        
        print("✅ 确认这是浏览器窗口")
        
        # 测试子控件访问
        print(f"\n🔧 第二步：测试子控件访问...")
        initial_access_success = False
        
        for i in range(windowChildCount):
            try:
                child = active_window.getChildAtIndex(i)
                if child:
                    child_name = getattr(child, 'name', 'unnamed')
                    child_role = child.getRoleName()
                    print(f"✅ 子控件 {i}: {child_name} ({child_role})")
                    initial_access_success = True
                else:
                    print(f"❌ 子控件 {i} 为空")
            except Exception as e:
                print(f"❌ 访问子控件 {i} 失败: {e}")
        
        if initial_access_success:
            print("✅ 子控件访问正常，无需强制刷新")
        else:
            print("❌ 子控件访问失败，需要强制刷新")
            
            print(f"\n🚀 第三步：执行强制刷新AT-SPI...")
            
            # 调用强制刷新方法
            refresh_success = uni._force_refresh_atspi_for_browser(active_window, 0)
            
            if refresh_success:
                print("✅ 强制刷新成功!")
                
                # 重新测试子控件访问
                print(f"\n🔧 第四步：重新测试子控件访问...")
                for i in range(active_window.childCount):
                    try:
                        child = active_window.getChildAtIndex(i)
                        if child:
                            child_name = getattr(child, 'name', 'unnamed')
                            child_role = child.getRoleName()
                            print(f"✅ 刷新后子控件 {i}: {child_name} ({child_role})")
                        else:
                            print(f"❌ 刷新后子控件 {i} 仍为空")
                    except Exception as e:
                        print(f"❌ 刷新后访问子控件 {i} 失败: {e}")
            else:
                print("❌ 强制刷新失败")
        
        # 最终测试完整的控件识别
        print(f"\n🎯 第五步：测试完整的控件识别...")
        
        start_time = time.time()
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        end_time = time.time()
        
        print(f"⏱️  识别耗时: {end_time - start_time:.3f}秒")
        
        if result and not result.get('error'):
            print("✅ 控件识别成功!")
            print(f"   控件名称: {result.get('Name', 'N/A')}")
            print(f"   控件类型: {result.get('Rolename', 'N/A')}")
            
            if 'button' in result.get('Rolename', '').lower():
                print("🔘 成功识别到按钮控件!")
                print("🎉 强制刷新AT-SPI功能正常工作!")
            else:
                print(f"❓ 识别到其他类型控件: {result.get('Rolename', 'N/A')}")
        else:
            print("❌ 控件识别失败!")
            if result:
                print(f"   错误: {result.get('error', '未知错误')}")
        
        # 性能测试
        print(f"\n📊 第六步：性能测试...")
        print("连续进行5次控件识别测试...")
        
        total_time = 0
        success_count = 0
        
        for i in range(5):
            start_time = time.time()
            result, _ = uni.kdk_getElement_Uni(x, y, False, False)  # 关闭调试输出
            end_time = time.time()
            
            test_time = end_time - start_time
            total_time += test_time
            
            if result and not result.get('error') and 'button' in result.get('Rolename', '').lower():
                success_count += 1
                print(f"   测试 {i+1}: ✅ 成功 ({test_time:.3f}秒)")
            else:
                print(f"   测试 {i+1}: ❌ 失败 ({test_time:.3f}秒)")
        
        avg_time = total_time / 5
        success_rate = (success_count / 5) * 100
        
        print(f"\n📈 性能统计:")
        print(f"   平均识别时间: {avg_time:.3f}秒")
        print(f"   成功率: {success_rate:.1f}% ({success_count}/5)")
        
        if success_rate >= 80:
            print("🎉 性能测试通过!")
        else:
            print("⚠️ 性能测试未达标，可能需要进一步优化")
            
    except Exception as e:
        print(f"💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_force_refresh()
