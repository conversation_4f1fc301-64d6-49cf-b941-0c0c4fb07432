#!/usr/bin/env python3
"""
测试浏览器子控件访问方法
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def test_browser_access():
    """测试浏览器子控件访问方法"""
    print("=== 测试浏览器子控件访问方法 ===")
    
    # 创建UNI实例
    uni = UNI()
    
    x, y = 187, 46
    
    try:
        # 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if active_window:
            window_name = getattr(active_window, 'name', '')
            print(f"🌐 活动窗口: {window_name}")
            print(f"   子控件数: {windowChildCount}")
            
            # 直接测试_access_browser_children方法
            print(f"\n🔧 直接测试_access_browser_children方法...")
            
            # 模拟递归函数
            test_candidates = []
            
            def mock_search_recursive(element, depth, parent_info):
                try:
                    import pyatspi
                    if hasattr(element, 'queryComponent'):
                        component = element.queryComponent()
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        
                        role = element.getRoleName().lower()
                        name = element.name if element.name else 'unnamed'
                        
                        # 检查坐标是否在元素范围内
                        contains_point = (extents.x <= x < extents.x + extents.width and
                                        extents.y <= y < extents.y + extents.height)
                        
                        if contains_point:
                            print(f"[TEST]   {'  ' * depth}找到控件: {name} ({role}) 深度:{depth}", file=sys.stderr)
                            test_candidates.append({
                                'name': name,
                                'role': role,
                                'depth': depth
                            })
                            
                            # 继续搜索子控件
                            for i in range(element.childCount):
                                try:
                                    child = element.getChildAtIndex(i)
                                    if child:
                                        mock_search_recursive(child, depth + 1, parent_info)
                                except:
                                    continue
                except Exception as e:
                    print(f"[TEST]   {'  ' * depth}搜索错误: {e}", file=sys.stderr)
            
            # 测试浏览器子控件访问
            result = uni._access_browser_children(active_window, 0, None, mock_search_recursive)
            
            print(f"\n📊 _access_browser_children结果: {result}")
            print(f"   找到的控件数: {len(test_candidates)}")
            
            for candidate in test_candidates:
                print(f"   - {candidate['name']} ({candidate['role']}) 深度:{candidate['depth']}")
                if 'button' in candidate['role']:
                    print(f"     🔘 这是按钮控件!")
            
            # 如果_access_browser_children失败，尝试直接调用智能深度搜索
            if not result:
                print(f"\n🧠 _access_browser_children失败，测试完整的智能深度搜索...")
                
                found_element = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)
                
                if found_element:
                    element_name = getattr(found_element, 'name', 'unnamed')
                    element_role = found_element.getRoleName()
                    print(f"✅ 智能深度搜索找到: {element_name} ({element_role})")
                    
                    if 'button' in element_role.lower():
                        print(f"🔘 成功找到按钮控件!")
                else:
                    print(f"❌ 智能深度搜索也失败了")
        else:
            print("❌ 未找到活动窗口")
            
    except Exception as e:
        print(f"💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_browser_access()
