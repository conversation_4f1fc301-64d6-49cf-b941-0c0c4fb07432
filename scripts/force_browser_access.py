#!/usr/bin/env python3
"""
强制访问浏览器子控件
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def force_browser_access():
    """强制访问浏览器子控件"""
    print("=== 强制访问浏览器子控件 ===")
    
    # 创建UNI实例
    uni = UNI()
    
    try:
        import pyatspi
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找浏览器窗口
        browser_window = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and '奇安信可信浏览器' in getattr(app, 'name', ''):
                    for j in range(app.childCount):
                        try:
                            window = app.getChildAtIndex(j)
                            if window and '新标签页' in getattr(window, 'name', ''):
                                browser_window = window
                                break
                        except:
                            continue
                    break
            except:
                continue
        
        if not browser_window:
            print("❌ 未找到浏览器窗口")
            return
        
        print(f"🌐 找到浏览器窗口: {getattr(browser_window, 'name', 'N/A')}")
        print(f"   子控件数量: {browser_window.childCount}")
        
        # 方法1: 强制刷新AT-SPI注册表
        print(f"\n🔄 方法1: 强制刷新AT-SPI注册表")
        try:
            # 清除所有缓存
            if hasattr(uni, 'desktop_cache'):
                uni.desktop_cache.clear()
            if hasattr(uni, 'window_cache'):
                uni.window_cache.clear()
            
            # 强制重新获取桌面
            time.sleep(0.5)
            desktop = pyatspi.Registry.getDesktop(0)
            
            # 重新查找浏览器窗口
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    if app and '奇安信可信浏览器' in getattr(app, 'name', ''):
                        for j in range(app.childCount):
                            try:
                                window = app.getChildAtIndex(j)
                                if window and '新标签页' in getattr(window, 'name', ''):
                                    browser_window = window
                                    print(f"   刷新后子控件数量: {browser_window.childCount}")
                                    
                                    # 尝试访问子控件
                                    for k in range(browser_window.childCount):
                                        try:
                                            child = browser_window.getChildAtIndex(k)
                                            if child:
                                                print(f"   ✅ 刷新后访问子控件 {k}: {getattr(child, 'name', 'unnamed')}")
                                            else:
                                                print(f"   ❌ 刷新后子控件 {k} 仍为空")
                                        except Exception as e:
                                            print(f"   ❌ 刷新后访问子控件 {k} 失败: {e}")
                                    break
                            except:
                                continue
                        break
                except:
                    continue
        except Exception as e:
            print(f"   ❌ 强制刷新失败: {e}")
        
        # 方法2: 尝试不同的AT-SPI接口
        print(f"\n🔧 方法2: 尝试不同的AT-SPI接口")
        try:
            # 尝试使用Selection接口
            if hasattr(browser_window, 'querySelection'):
                try:
                    selection = browser_window.querySelection()
                    if selection:
                        print(f"   ✅ 找到Selection接口")
                        for i in range(selection.nSelectedChildren):
                            try:
                                selected_child = selection.getSelectedChild(i)
                                if selected_child:
                                    print(f"   ✅ Selection子控件 {i}: {getattr(selected_child, 'name', 'unnamed')}")
                            except:
                                continue
                except:
                    print(f"   ❌ Selection接口不可用")
            
            # 尝试使用Action接口
            if hasattr(browser_window, 'queryAction'):
                try:
                    action = browser_window.queryAction()
                    if action:
                        print(f"   ✅ 找到Action接口，动作数: {action.nActions}")
                        for i in range(action.nActions):
                            try:
                                action_name = action.getName(i)
                                print(f"   动作 {i}: {action_name}")
                            except:
                                continue
                except:
                    print(f"   ❌ Action接口不可用")
            
            # 尝试使用Table接口
            if hasattr(browser_window, 'queryTable'):
                try:
                    table = browser_window.queryTable()
                    if table:
                        print(f"   ✅ 找到Table接口")
                except:
                    print(f"   ❌ Table接口不可用")
                    
        except Exception as e:
            print(f"   ❌ AT-SPI接口测试失败: {e}")
        
        # 方法3: 尝试通过坐标直接查找
        print(f"\n🎯 方法3: 通过坐标直接查找")
        try:
            x, y = 187, 46
            
            # 使用pyatspi的坐标查找功能
            try:
                desktop = pyatspi.Registry.getDesktop(0)
                element_at_point = desktop.getAccessibleAtPoint(x, y, pyatspi.DESKTOP_COORDS)
                if element_at_point:
                    element_name = getattr(element_at_point, 'name', 'unnamed')
                    element_role = element_at_point.getRoleName()
                    print(f"   ✅ 坐标查找到: {element_name} ({element_role})")
                    
                    if 'button' in element_role.lower():
                        print(f"   🔘 找到按钮控件!")
                else:
                    print(f"   ❌ 坐标查找失败")
            except Exception as e:
                print(f"   ❌ 坐标查找异常: {e}")
                
        except Exception as e:
            print(f"   ❌ 坐标查找方法失败: {e}")
        
        # 方法4: 检查浏览器进程和权限
        print(f"\n🔒 方法4: 检查浏览器进程和权限")
        try:
            import subprocess
            
            # 检查浏览器进程
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            browser_processes = [line for line in result.stdout.split('\n') if 'qaxbrowser' in line.lower()]
            
            print(f"   浏览器进程数: {len(browser_processes)}")
            for i, process in enumerate(browser_processes[:3]):  # 只显示前3个
                print(f"   进程 {i+1}: {process.strip()}")
            
            # 检查AT-SPI环境变量
            atspi_env = os.environ.get('AT_SPI_BUS_ADDRESS', 'Not set')
            print(f"   AT_SPI_BUS_ADDRESS: {atspi_env}")
            
        except Exception as e:
            print(f"   ❌ 进程检查失败: {e}")
            
    except Exception as e:
        print(f"💥 强制访问过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    force_browser_access()
