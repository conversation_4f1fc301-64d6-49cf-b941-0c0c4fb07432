#!/usr/bin/env python3
"""
验证浏览器AT-SPI是否正常
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def verify_browser_atspi():
    """验证浏览器AT-SPI是否正常"""
    print("=== 验证浏览器AT-SPI是否正常 ===")
    
    # 创建UNI实例
    uni = UNI()
    
    try:
        import pyatspi
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        if not desktop:
            print("❌ 无法获取桌面对象")
            return
        
        # 查找浏览器应用
        browser_app = None
        browser_window = None
        
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if not app:
                    continue
                    
                app_name = getattr(app, 'name', 'Unknown')
                if '奇安信可信浏览器' in app_name:
                    browser_app = app
                    print(f"🌐 找到浏览器应用: {app_name}")
                    
                    # 获取浏览器窗口
                    for j in range(app.childCount):
                        try:
                            window = app.getChildAtIndex(j)
                            if window and '新标签页' in getattr(window, 'name', ''):
                                browser_window = window
                                print(f"✅ 找到浏览器窗口: {getattr(window, 'name', 'N/A')}")
                                break
                        except Exception as e:
                            print(f"❌ 访问窗口 {j} 失败: {e}")
                    break
            except Exception as e:
                print(f"❌ 访问应用 {i} 失败: {e}")
        
        if not browser_window:
            print("❌ 未找到浏览器窗口")
            return
        
        print(f"\n🔍 详细检查浏览器窗口子控件...")
        print(f"   窗口名称: {getattr(browser_window, 'name', 'N/A')}")
        print(f"   窗口角色: {browser_window.getRoleName()}")
        print(f"   子控件数量: {browser_window.childCount}")
        
        # 方法1: 直接getChildAtIndex
        print(f"\n方法1: 直接getChildAtIndex")
        for i in range(browser_window.childCount):
            try:
                child = browser_window.getChildAtIndex(i)
                if child:
                    child_name = getattr(child, 'name', 'unnamed')
                    child_role = child.getRoleName()
                    print(f"  ✅ 子控件 {i}: {child_name} ({child_role})")
                    print(f"     子控件数量: {child.childCount}")
                    
                    # 继续检查孙控件
                    if child.childCount > 0:
                        print(f"     🔍 检查孙控件...")
                        for k in range(min(child.childCount, 5)):
                            try:
                                grandchild = child.getChildAtIndex(k)
                                if grandchild:
                                    gc_name = getattr(grandchild, 'name', 'unnamed')
                                    gc_role = grandchild.getRoleName()
                                    print(f"       孙控件 {k}: {gc_name} ({gc_role})")
                                    
                                    # 检查是否有更深层的控件
                                    if grandchild.childCount > 0:
                                        print(f"         继续深入...")
                                        for l in range(min(grandchild.childCount, 3)):
                                            try:
                                                ggchild = grandchild.getChildAtIndex(l)
                                                if ggchild:
                                                    ggc_name = getattr(ggchild, 'name', 'unnamed')
                                                    ggc_role = ggchild.getRoleName()
                                                    print(f"           曾孙控件 {l}: {ggc_name} ({ggc_role})")
                                                    
                                                    # 检查按钮
                                                    if 'button' in ggc_role.lower():
                                                        print(f"           🔘 找到按钮: {ggc_name}")
                                                        
                                                        # 获取按钮位置
                                                        try:
                                                            component = ggchild.queryComponent()
                                                            if component:
                                                                extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                                                print(f"             位置: ({extents.x}, {extents.y})")
                                                                print(f"             大小: {extents.width}x{extents.height}")
                                                                
                                                                # 检查坐标(187, 46)是否在按钮内
                                                                if (extents.x <= 187 <= extents.x + extents.width and 
                                                                    extents.y <= 46 <= extents.y + extents.height):
                                                                    print(f"             ✅ 坐标(187, 46)在此按钮内!")
                                                        except Exception as e:
                                                            print(f"             ❌ 获取按钮位置失败: {e}")
                                            except Exception as e:
                                                print(f"           ❌ 访问曾孙控件 {l} 失败: {e}")
                            except Exception as e:
                                print(f"       ❌ 访问孙控件 {k} 失败: {e}")
                else:
                    print(f"  ❌ 子控件 {i} 为空")
            except Exception as e:
                print(f"  ❌ 访问子控件 {i} 失败: {e}")
        
        # 方法2: 使用迭代器
        print(f"\n方法2: 使用迭代器")
        try:
            for i, child in enumerate(browser_window):
                if i >= browser_window.childCount:
                    break
                if child:
                    child_name = getattr(child, 'name', 'unnamed')
                    child_role = child.getRoleName()
                    print(f"  ✅ 迭代器子控件 {i}: {child_name} ({child_role})")
                else:
                    print(f"  ❌ 迭代器子控件 {i} 为空")
                if i >= 5:  # 限制数量
                    break
        except Exception as e:
            print(f"  ❌ 迭代器方法失败: {e}")
            
    except Exception as e:
        print(f"💥 验证过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_browser_atspi()
