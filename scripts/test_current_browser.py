#!/usr/bin/env python3
"""
测试当前浏览器控件识别状态
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def test_current_browser():
    """测试当前浏览器控件识别状态"""
    print("=== 测试当前浏览器控件识别状态 ===")
    
    # 创建UNI实例
    uni = UNI()
    
    # 测试坐标 (187, 46) - 首页按钮
    x, y = 187, 46
    
    print(f"🎯 测试坐标 ({x}, {y}) 的控件识别...")
    
    try:
        # 使用UNI进行控件识别
        start_time = time.time()
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        end_time = time.time()
        
        print(f"⏱️  识别耗时: {end_time - start_time:.3f}秒")
        print(f"📝 信息文本: {info_text}")
        
        if result and not result.get('error'):
            print("✅ 控件识别成功!")
            print(f"  控件名称: {result.get('Name', 'N/A')}")
            print(f"  控件类型: {result.get('Rolename', 'N/A')}")
            print(f"  进程名称: {result.get('ProcessName', 'N/A')}")
            
            coords = result.get('Coords', {})
            if coords:
                print(f"  控件位置: x={coords.get('x', 0)}, y={coords.get('y', 0)}")
                print(f"  控件大小: 宽={coords.get('width', 0)}, 高={coords.get('height', 0)}")
            
            # 检查是否为首页按钮
            name = result.get('Name', '').lower()
            role = result.get('Rolename', '').lower()
            
            if 'button' in role and ('首页' in name or 'home' in name):
                print("🏠 ✅ 成功识别到首页按钮!")
            elif 'button' in role:
                print(f"🔘 识别到按钮控件: {result.get('Name', 'N/A')}")
            elif 'frame' in role:
                print("🖼️ ❌ 只识别到frame控件，未深入到按钮层级")
            else:
                print(f"❓ 识别到其他控件: {name} ({role})")
                
        else:
            print("❌ 控件识别失败!")
            if result:
                print(f"  错误信息: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_current_browser()
