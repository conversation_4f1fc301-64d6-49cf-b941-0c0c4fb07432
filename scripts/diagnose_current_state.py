#!/usr/bin/env python3
"""
诊断当前窗口状态
"""
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from scripts.UNI import UNI

def diagnose_current_state():
    """诊断当前窗口状态"""
    print("=== 诊断当前窗口状态 ===")
    
    # 创建UNI实例
    uni = UNI()
    
    x, y = 187, 46
    
    try:
        import pyatspi
        
        print("🔍 第一步：检查所有应用程序...")
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        print(f"   桌面应用数量: {desktop.childCount}")
        
        # 查找所有应用程序
        all_apps = []
        browser_apps = []
        
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if not app:
                    continue
                    
                app_name = getattr(app, 'name', 'Unknown')
                all_apps.append(f"{i}: {app_name}")
                
                if '浏览器' in app_name or 'browser' in app_name.lower() or 'qax' in app_name.lower():
                    browser_apps.append({
                        'index': i,
                        'name': app_name,
                        'app': app
                    })
                    print(f"   🌐 找到可能的浏览器应用 {i}: {app_name}")
            except Exception as e:
                all_apps.append(f"{i}: 访问失败 - {e}")
        
        print(f"\n📋 所有应用程序列表:")
        for app_info in all_apps:
            print(f"   {app_info}")
        
        if not browser_apps:
            print("\n❌ 未找到浏览器应用")
            print("💡 请确保浏览器已经打开并且窗口可见")
            return
        
        print(f"\n🔍 第二步：检查浏览器应用的窗口...")
        
        target_windows = []
        
        for browser_info in browser_apps:
            app = browser_info['app']
            app_name = browser_info['name']
            
            print(f"\n   浏览器应用: {app_name}")
            print(f"   子窗口数量: {app.childCount}")
            
            for j in range(app.childCount):
                try:
                    window = app.getChildAtIndex(j)
                    if not window:
                        print(f"     窗口 {j}: 为空")
                        continue
                    
                    window_name = getattr(window, 'name', 'Unknown')
                    window_role = window.getRoleName()
                    
                    print(f"     窗口 {j}: {window_name} ({window_role})")
                    
                    # 获取窗口状态和位置
                    try:
                        states = window.getState()
                        state_names = [pyatspi.stateToString(state) for state in states.getStates()]
                        print(f"       状态: {', '.join(state_names)}")
                        
                        component = window.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            print(f"       位置: ({extents.x}, {extents.y}) 大小: {extents.width}x{extents.height}")
                            
                            # 检查坐标是否在窗口内
                            if (extents.x <= x <= extents.x + extents.width and 
                                extents.y <= y <= extents.y + extents.height):
                                print(f"       ✅ 坐标({x}, {y})在此窗口内!")
                                target_windows.append({
                                    'window': window,
                                    'name': window_name,
                                    'app_name': app_name,
                                    'extents': extents,
                                    'states': state_names
                                })
                    except Exception as e:
                        print(f"       ❌ 获取窗口信息失败: {e}")
                        
                except Exception as e:
                    print(f"     ❌ 访问窗口 {j} 失败: {e}")
        
        if not target_windows:
            print(f"\n❌ 未找到包含坐标({x}, {y})的窗口")
            return
        
        print(f"\n🎯 第三步：测试目标窗口的子控件访问...")
        
        for i, window_info in enumerate(target_windows):
            window = window_info['window']
            window_name = window_info['name']
            
            print(f"\n   目标窗口 {i+1}: {window_name}")
            print(f"   子控件数量: {window.childCount}")
            
            # 测试子控件访问
            for j in range(window.childCount):
                try:
                    print(f"     尝试访问子控件 {j}...")
                    child = window.getChildAtIndex(j)
                    if child:
                        child_name = getattr(child, 'name', 'unnamed')
                        child_role = child.getRoleName()
                        print(f"     ✅ 子控件 {j}: {child_name} ({child_role})")
                        print(f"       子控件数量: {child.childCount}")
                        
                        # 如果有子控件，继续深入
                        if child.childCount > 0:
                            print(f"       🔍 深入检查...")
                            for k in range(min(child.childCount, 3)):
                                try:
                                    grandchild = child.getChildAtIndex(k)
                                    if grandchild:
                                        gc_name = getattr(grandchild, 'name', 'unnamed')
                                        gc_role = grandchild.getRoleName()
                                        print(f"         孙控件 {k}: {gc_name} ({gc_role})")
                                        
                                        if 'button' in gc_role.lower():
                                            print(f"         🔘 找到按钮!")
                                except Exception as e:
                                    print(f"         ❌ 访问孙控件 {k} 失败: {e}")
                    else:
                        print(f"     ❌ 子控件 {j} 为空")
                        print(f"     🚨 这是需要修复的问题!")
                        
                        # 尝试强制修复
                        print(f"     🔧 尝试强制修复...")
                        
                        # 调用我们的修复方法
                        success = uni._restart_atspi_connection(window, 0)
                        if success:
                            print(f"     ✅ 修复成功，重新测试...")
                            child = window.getChildAtIndex(j)
                            if child:
                                child_name = getattr(child, 'name', 'unnamed')
                                child_role = child.getRoleName()
                                print(f"     ✅ 修复后子控件 {j}: {child_name} ({child_role})")
                            else:
                                print(f"     ❌ 修复后子控件 {j} 仍为空")
                        else:
                            print(f"     ❌ 修复失败")
                            
                except Exception as e:
                    print(f"     ❌ 访问子控件 {j} 异常: {e}")
        
        # 测试UNI的窗口选择
        print(f"\n🎯 第四步：测试UNI的窗口选择...")
        
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if active_window:
            selected_window_name = getattr(active_window, 'name', '')
            print(f"   UNI选择的窗口: {selected_window_name}")
            print(f"   进程ID: {processid}")
            print(f"   窗口角色: {windowRoleName}")
            
            # 检查是否选择了正确的窗口
            is_correct = any(selected_window_name == w['name'] for w in target_windows)
            print(f"   是否选择了正确的窗口: {is_correct}")
        else:
            print("   ❌ UNI未找到活动窗口")
            
    except Exception as e:
        print(f"💥 诊断过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_current_state()
